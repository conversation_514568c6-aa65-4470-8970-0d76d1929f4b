import React, {useState, useContext, useEffect} from 'react';
import {
    Card,
    CardContent,
    Typography,
    Grid,
    Box,
    Container,
    CardMedia,
    IconButton,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Button,
    RadioGroup,
    FormControlLabel,
    Radio, CircularProgress
} from '@mui/material';
import DownloadIcon from '@mui/icons-material/Download';
import { useTheme, useMediaQuery } from '@mui/material';
import {useNotifications} from "@toolpad/core";
import {webdavServiceUrl} from "@/src/app/[locale]/config";
import {useTranslations} from "next-intl";
import {GlobalContext} from "@/src/app/[locale]/page";


export const DownloadDialog = ({ open, onClose, song }) => {
    const [format, setFormat] = useState('mp3');
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [selectedConfig, setSelectedConfig] = useState(null);
    const [selectedConfigId, setSelectedConfigId] = useState(null)
    const notifications = useNotifications();
    const { webdavConfigs, webdavJwt, premium } = useContext(GlobalContext)
    const t = useTranslations("Download")

    const handleDownload = () => {
        // Open new tab with query string
        const queryParams = new URLSearchParams({
            title: song.title,
            album: song.album,
            artist: song.artist,
            videoId: song.videoId,
            request_format: format,
            song_hash: song.song_hash,
            thumbnail: song.thumbnail
        }).toString();

        window.open(`/download?${queryParams}`, '_blank');

        onClose();
    };

    const handleWebDavUpload = async () => {
        if (!selectedConfigId) {
            alert(t("select_webdav"));
            return;
        }

        if (!song || !song.title || !song.album || !song.artist || !song.videoId) {
            alert(t("incomplete_song_info"));
            return;
        }

        // 找到选中的WebDAV配置
        // 将字符串转换为数字进行比较，因为后端返回的id是数字类型
        const selectedWebdavConfig = webdavConfigs.find(config => config.id === parseInt(selectedConfigId));
        if (!selectedWebdavConfig) {
            alert(t("webdav_config_not_found"));
            return;
        }

        setIsLoading(true);
        setError(null);

        // 构造WebDAV服务需要的数据
        const uploadData = {
            song_hash: song.song_hash,
            song_title: song.title,
            song_artist: song.artist,
            album: song.album,
            video_id: song.videoId,
            format: format,
            cover_url: song.thumbnail, // 直接使用封面URL
            jwt_token: webdavJwt, // 添加JWT令牌
            webdav_config: {
                id: selectedWebdavConfig.id,
                url: selectedWebdavConfig.url,
                username: selectedWebdavConfig.username,
                password: selectedWebdavConfig.password,
                signature: selectedWebdavConfig.signature
            }
        };

        try {
            // 直接调用WebDAV服务
            const response = await fetch(webdavServiceUrl + 'upload', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(uploadData),
                credentials:'include'
            });

            const result = await response.json();
            if (!response.ok) {
                throw new Error(result.error || t("upload_error"));
            }

            notifications.show(t("upload_success", {title: song.title}), {
                autoHideDuration: 2000
            });
            setIsLoading(false);
            onClose();
        } catch (err) {
            setError(err.message);
            setIsLoading(false);
        }
    };

    const handleWebDavConfigChange = (e) => {
        const configId = e.target.value;
        // 将字符串转换为数字进行比较，因为后端返回的id是数字类型
        const selected = webdavConfigs.find(config => config.id === parseInt(configId));
        setSelectedConfig(selected)
        setSelectedConfigId(configId);
    };

     return (
        <Dialog onClick={event => event.stopPropagation()} open={open} onClose={onClose}>
            <DialogTitle>{t("download_song",{title: song.title})}</DialogTitle>
            {isLoading? <Box display="flex" justifyContent="center" alignItems="center" height='80px'>
                <CircularProgress />
            </Box> : <DialogContent>
                <RadioGroup value={format} onChange={(e) => setFormat(e.target.value)}>
                    <FormControlLabel value="mp3" control={<Radio />} label="MP3" />
                    <FormControlLabel value="flac" control={<Radio />} label="FLAC" />
                </RadioGroup>
                {webdavConfigs.length > 0 && (
                    <>
                        <Typography variant="subtitle1">{t("select_webdav_config")}</Typography>
                        <RadioGroup value={selectedConfigId || ''} onChange={handleWebDavConfigChange}>
                            {webdavConfigs.map((config, index) => (
                                <FormControlLabel key={index} value={config.id} control={<Radio />} label={config.url} />
                            ))}
                        </RadioGroup>
                    </>
                )}
                {error && <Typography color="error">{error}</Typography>}
            </DialogContent>}
            <DialogActions>
                <Button aria-label={premium ? "premium" : "no_premium"} onClick={handleDownload} disabled={isLoading}>{t("download")}</Button>
                <Button onClick={handleWebDavUpload} disabled={isLoading || webdavConfigs.length < 1}>{t("upload_to_webdav")}</Button>
                <Button onClick={onClose} disabled={isLoading}>{t("cancel")}</Button>
            </DialogActions>
        </Dialog>
    );
};

// 音乐卡片组件
const MusicCard = ({ coverHeight,song }) => {
    const [dialogOpen, setDialogOpen] = useState(false);
    const {setPlayingSongData} = useContext(GlobalContext)
    const theme = useTheme();
    const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));

    const previewHandle = () => {
        setPlayingSongData(song)
    }
    return (
        <Card
            sx={{
                minWidth: 275,
                borderRadius: isSmallScreen ? 0 : 2,
                boxShadow: 'none',
                display: 'flex',
                alignItems: 'center',
                backgroundColor: 'transparent',
                border: isSmallScreen ? 'none' : `1px solid ${theme.palette.divider}`,
                borderBottom: `1px solid ${theme.palette.divider}`,
                overflow: 'hidden',
                margin: isSmallScreen ? 0 : 0.5,
                padding: isSmallScreen ? '8px 0' : 0,
                position: 'relative', // Added to ensure proper positioning context
                cursor: 'pointer', // 添加指针光标
                transition: 'all 0.2s ease-in-out', // 添加平滑过渡效果
                '&:hover': {
                    backgroundColor: isSmallScreen
                        ? theme.palette.mode === 'dark'
                            ? 'rgba(255, 255, 255, 0.08)'
                            : 'rgba(0, 0, 0, 0.04)'
                        : theme.palette.background.paper,
                    boxShadow: isSmallScreen ? 'none' : '0 6px 20px rgba(0, 0, 0, 0.15)',
                    transform: isSmallScreen ? 'none' : 'translateY(-2px)', // 轻微上移效果
                    borderColor: isSmallScreen ? theme.palette.divider : theme.palette.primary.main, // 边框颜色变化
                },
                '&:active': {
                    transform: isSmallScreen ? 'none' : 'translateY(0px)', // 点击时回到原位
                    boxShadow: isSmallScreen ? 'none' : '0 2px 8px rgba(0, 0, 0, 0.1)',
                },
            }}
            onClick={() => previewHandle()}
        >
            <CardMedia
                component="img"
                sx={{
                    width: coverHeight,
                    height: coverHeight,
                    objectFit: 'cover',
                    borderRadius: isSmallScreen ? 1 : 1,
                    margin: isSmallScreen ? '0 12px 0 12px' : 1,
                    flexShrink: 0, // Prevent image from shrinking
                }}
                image={song.thumbnail}
                alt={song.title}
            />
            <CardContent
                sx={{
                    flex: 1,
                    padding: isSmallScreen ? '0 48px 0 0' : '12px 48px 12px 12px',
                    minWidth: 0, // Enable text truncation
                    overflow: 'hidden' // Ensure content doesn't overflow
                }}
            >
                <Typography
                    variant="h6"
                    sx={{
                        color: 'theme.palette.text.primary',
                        fontWeight: 'bold',
                        fontSize: '1rem',
                        lineHeight: 1.4,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                    }}
                >
                    {song.title}
                </Typography>
                <Typography
                    variant="body2"
                    sx={{
                        color: '#666',
                        marginTop: 0.5,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                    }}
                >
                    {song.artist} · {song.album}
                </Typography>
                <Typography variant="caption" sx={{ color: '#999', marginTop: 0.5 }}>
                    {song.duration}
                </Typography>
            </CardContent>
            <IconButton
                color='inherit'
                sx={{
                    position: 'absolute',
                    top: 4,
                    right: 1,
                    zIndex: 10,
                }}
                onClick={(e) => {
                    setDialogOpen(true);
                    e.stopPropagation();
                }}
            >
                <DownloadIcon />
            </IconButton>
            <DownloadDialog open={dialogOpen} onClose={() => setDialogOpen(false)} song={song} />
        </Card>
    );
};

// 音乐卡片容器组件
export const MusicCardContainer = ({ songs,loading }) => {
    const theme = useTheme();

    // 根据屏幕宽度动态设置列数
    const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
    const isMediumScreen = useMediaQuery(theme.breakpoints.between('sm', 'md'));
    const columns = isSmallScreen ? 1 : isMediumScreen ? 2 : 3;
    const coverHeight = isSmallScreen ? 60 : 80

    return (
        <Container sx={{
            position: 'relative',
            padding: isSmallScreen ? '0 !important' : undefined,
            maxWidth: isSmallScreen ? 'none !important' : undefined
        }}>
            {loading && (
                <Box
                    sx={(theme) => ({
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        zIndex: 10,
                        backgroundColor: theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.8)' : 'rgba(255, 255, 255, 0.6)', // 根据theme模式调整背景色
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'flex-start'  // 修改为顶部对齐
                    })}
                >
                    <CircularProgress sx={{ marginTop: 16 }} />
                </Box>
            )}
            <Box sx={{
                flexGrow: 1,
                mt: isSmallScreen ? 0 : 1,
                opacity: loading ? 0.5 : 1,
                mx: 0
            }}>
                <Grid container spacing={isSmallScreen ? 0 : 0.5}>
                    {songs.map((song, index) => (
                        <Grid item xs={12 / columns} key={index}>
                            <MusicCard coverHeight={coverHeight} song={song} />
                        </Grid>
                    ))}
                </Grid>
            </Box>
        </Container>
    );
};

